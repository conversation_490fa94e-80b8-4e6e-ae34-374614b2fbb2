#!/usr/bin/env python
"""
Test script to verify that the HrTask import fix works correctly.
This script tests the bug fix for HrTask and EmployeeTask import errors in alert_views.py.
"""

import os
import sys
import django

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ElDawliya_sys.settings')
django.setup()

def test_hrtask_import_fix():
    """Test that HrTask and EmployeeTask imports work correctly in alert_views.py"""
    print("🧪 Testing HrTask Import Fix")
    print("=" * 50)
    
    try:
        # Test direct model imports
        print("1. Testing direct model imports...")
        from Hr.models.hr_task_models import HrTaskNew as HrTask
        print(f"   ✅ HrTaskNew imported as HrTask: {HrTask.__name__}")
        print(f"   📋 Model table: {HrTask._meta.db_table}")
        
        from Hr.models.task_models import HrEmployeeTask as EmployeeTask
        print(f"   ✅ HrEmployeeTask imported as EmployeeTask: {EmployeeTask.__name__}")
        print(f"   📋 Model table: {EmployeeTask._meta.db_table}")
        
        # Test alert_views import
        print("\n2. Testing alert_views.py imports...")
        from Hr.views.alert_views import alert_list
        print("   ✅ alert_list function imported successfully")
        
        # Test that the models can be used
        print("\n3. Testing model functionality...")
        task_count = HrTask.objects.count()
        employee_task_count = EmployeeTask.objects.count()
        print(f"   📊 HrTask count: {task_count}")
        print(f"   📊 EmployeeTask count: {employee_task_count}")
        
        print("\n🎉 SUCCESS: All HrTask import fixes work correctly!")
        return True
        
    except ImportError as e:
        print(f"\n❌ Import Error: {e}")
        return False
    except Exception as e:
        print(f"\n❌ Unexpected Error: {e}")
        return False

if __name__ == "__main__":
    success = test_hrtask_import_fix()
    if success:
        print("\n✅ Test completed successfully!")
        sys.exit(0)
    else:
        print("\n❌ Test failed!")
        sys.exit(1)
