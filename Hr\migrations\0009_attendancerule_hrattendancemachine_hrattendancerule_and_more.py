# Generated by Django 5.0.14 on 2025-07-09 13:12

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('Hr', '0008_auto_20250708_2043'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AttendanceRule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=250, verbose_name='اسم القاعدة')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
            ],
            options={
                'verbose_name': 'قاعدة الحضور',
                'verbose_name_plural': 'قواعد الحضور',
                'db_table': 'Tbl_Attendance_Rule',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='HrAttendanceMachine',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الماكينة')),
                ('ip_address', models.CharField(max_length=15, verbose_name='عنوان IP')),
                ('port', models.PositiveIntegerField(default=4370, verbose_name='المنفذ')),
                ('machine_type', models.CharField(choices=[('in', 'حضور'), ('out', 'انصراف'), ('both', 'حضور وانصراف')], max_length=10, verbose_name='نوع الماكينة')),
                ('location', models.CharField(blank=True, max_length=100, null=True, verbose_name='الموقع')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'ماكينة الحضور',
                'verbose_name_plural': 'ماكينات الحضور',
                'db_table': 'Hr_AttendanceMachine',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='HrAttendanceRule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم القاعدة')),
                ('description', models.TextField(blank=True, null=True, verbose_name='وصف')),
                ('work_schedule', models.JSONField(verbose_name='جدول العمل')),
                ('late_grace_minutes', models.PositiveIntegerField(default=0, verbose_name='فترة سماح التأخير (دقائق)')),
                ('early_leave_grace_minutes', models.PositiveIntegerField(default=0, verbose_name='فترة سماح الانصراف المبكر (دقائق)')),
                ('weekly_off_days', models.JSONField(default=list, verbose_name='أيام الإجازة الأسبوعية')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'قاعدة الحضور',
                'verbose_name_plural': 'قواعد الحضور',
                'db_table': 'Hr_AttendanceRule',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='HrCar',
            fields=[
                ('car_id', models.IntegerField(primary_key=True, serialize=False, verbose_name='رقم السيارة')),
                ('car_name', models.CharField(blank=True, max_length=50, null=True, verbose_name='اسم السيارة')),
                ('car_type', models.CharField(blank=True, max_length=50, null=True, verbose_name='نوع السيارة')),
                ('car_salary', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='تكلفة السيارة')),
                ('car_salary_farda', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='تكلفة السيارة (فردة)')),
                ('supplier', models.CharField(blank=True, max_length=50, null=True, verbose_name='المورد')),
                ('contract_type', models.CharField(blank=True, max_length=50, null=True, verbose_name='نوع العقد')),
                ('car_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='رقم السيارة')),
                ('car_license_expiration_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ انتهاء رخصة السيارة')),
                ('driver_name', models.CharField(blank=True, max_length=50, null=True, verbose_name='اسم السائق')),
                ('driver_phone', models.CharField(blank=True, max_length=50, null=True, verbose_name='رقم هاتف السائق')),
                ('driver_license_expiration_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ انتهاء رخصة السائق')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('shift_type', models.CharField(blank=True, choices=[('حضور فقط', 'حضور فقط'), ('انصراف فقط', 'انصراف فقط')], max_length=50, null=True, verbose_name='نوع الوردية')),
                ('contract_type_farada', models.CharField(blank=True, max_length=50, null=True, verbose_name='نوع العقد (فردة)')),
            ],
            options={
                'verbose_name': 'السيارة',
                'verbose_name_plural': 'السيارات',
                'db_table': 'Hr_Car',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='Job',
            fields=[
                ('jop_code', models.IntegerField(primary_key=True, serialize=False, verbose_name='رمز الوظيفة')),
                ('jop_name', models.CharField(max_length=250, verbose_name='اسم الوظيفة')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('note', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
            ],
            options={
                'verbose_name': 'الوظيفة',
                'verbose_name_plural': 'الوظائف',
                'db_table': 'Tbl_Job',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='JobInsurance',
            fields=[
                ('job_code_insurance', models.IntegerField(primary_key=True, serialize=False, verbose_name='رمز وظيفة التأمين')),
                ('job_name_insurance', models.CharField(max_length=250, verbose_name='اسم وظيفة التأمين')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('note', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
            ],
            options={
                'verbose_name': 'وظيفة التأمين',
                'verbose_name_plural': 'وظائف التأمين',
                'db_table': 'Tbl_Job_Insurance',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='LegacyDepartment',
            fields=[
                ('dept_code', models.IntegerField(primary_key=True, serialize=False, verbose_name='رمز القسم')),
                ('dept_name', models.CharField(max_length=250, verbose_name='اسم القسم')),
                ('manager_id', models.IntegerField(blank=True, null=True, verbose_name='كود مدير القسم')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('note', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
            ],
            options={
                'verbose_name': 'القسم (قديم)',
                'verbose_name_plural': 'الأقسام (قديمة)',
                'db_table': 'Tbl_Department',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='OfficialHoliday',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=250, verbose_name='اسم العطلة')),
                ('date', models.DateField(verbose_name='التاريخ')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
            ],
            options={
                'verbose_name': 'العطلة الرسمية',
                'verbose_name_plural': 'العطل الرسمية',
                'db_table': 'Tbl_Official_Holiday',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='SalaryItem',
            fields=[
                ('item_code', models.CharField(max_length=50, primary_key=True, serialize=False, verbose_name='رمز البند')),
                ('name', models.CharField(max_length=250, verbose_name='اسم البند')),
                ('type', models.CharField(max_length=50, verbose_name='نوع البند')),
                ('default_value', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='القيمة الافتراضية')),
                ('is_auto_applied', models.BooleanField(default=False, verbose_name='تطبيق تلقائي')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
            ],
            options={
                'verbose_name': 'بند الراتب',
                'verbose_name_plural': 'بنود الرواتب',
                'db_table': 'Tbl_Salary_Item',
                'managed': True,
            },
        ),
        migrations.AlterModelOptions(
            name='employeetask',
            options={'managed': True, 'verbose_name': 'مهمة الموظف', 'verbose_name_plural': 'مهام الموظفين'},
        ),
        migrations.RemoveField(
            model_name='car',
            name='car_license_expiration_date',
        ),
        migrations.RemoveField(
            model_name='car',
            name='car_number',
        ),
        migrations.RemoveField(
            model_name='car',
            name='car_salary',
        ),
        migrations.RemoveField(
            model_name='car',
            name='car_salary_farda',
        ),
        migrations.RemoveField(
            model_name='car',
            name='contract_type',
        ),
        migrations.RemoveField(
            model_name='car',
            name='contract_type_farada',
        ),
        migrations.RemoveField(
            model_name='car',
            name='driver_license_expiration_date',
        ),
        migrations.RemoveField(
            model_name='car',
            name='driver_name',
        ),
        migrations.RemoveField(
            model_name='car',
            name='driver_phone',
        ),
        migrations.RemoveField(
            model_name='car',
            name='shift_type',
        ),
        migrations.RemoveField(
            model_name='employeetask',
            name='completion_date',
        ),
        migrations.RemoveField(
            model_name='employeetask',
            name='created_at',
        ),
        migrations.RemoveField(
            model_name='employeetask',
            name='notes',
        ),
        migrations.RemoveField(
            model_name='employeetask',
            name='progress',
        ),
        migrations.RemoveField(
            model_name='employeetask',
            name='start_date',
        ),
        migrations.RemoveField(
            model_name='employeetask',
            name='updated_at',
        ),
        migrations.AddField(
            model_name='car',
            name='note',
            field=models.TextField(blank=True, null=True, verbose_name='ملاحظات'),
        ),
        migrations.AlterField(
            model_name='car',
            name='car_id',
            field=models.CharField(max_length=50, primary_key=True, serialize=False, verbose_name='رقم السيارة'),
        ),
        migrations.AlterField(
            model_name='car',
            name='car_name',
            field=models.CharField(default=django.utils.timezone.now, max_length=250, verbose_name='اسم السيارة'),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='car',
            name='car_type',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='نوع السيارة'),
        ),
        migrations.AlterField(
            model_name='car',
            name='supplier',
            field=models.CharField(blank=True, max_length=250, null=True, verbose_name='المورد'),
        ),
        migrations.AlterField(
            model_name='employeetask',
            name='assigned_by',
            field=models.ForeignKey(default=django.utils.timezone.now, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أسند بواسطة'),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='employeetask',
            name='description',
            field=models.TextField(blank=True, null=True, verbose_name='الوصف'),
        ),
        migrations.AlterField(
            model_name='employeetask',
            name='employee',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Hr.employee', verbose_name='الموظف'),
        ),
        migrations.AlterField(
            model_name='employeetask',
            name='priority',
            field=models.CharField(max_length=50, verbose_name='الأولوية'),
        ),
        migrations.AlterField(
            model_name='employeetask',
            name='status',
            field=models.CharField(max_length=50, verbose_name='الحالة'),
        ),
        migrations.AlterField(
            model_name='employeetask',
            name='title',
            field=models.CharField(max_length=250, verbose_name='العنوان'),
        ),
        migrations.AlterModelTable(
            name='employeetask',
            table='Tbl_Employee_Task',
        ),
        migrations.CreateModel(
            name='EmployeeAttendanceRule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('effective_date', models.DateField(verbose_name='تاريخ السريان')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('attendance_rule', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Hr.attendancerule', verbose_name='قاعدة الحضور')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Hr.employee', verbose_name='الموظف')),
            ],
            options={
                'verbose_name': 'قاعدة حضور الموظف',
                'verbose_name_plural': 'قواعد حضور الموظفين',
                'db_table': 'Tbl_Employee_Attendance_Rule',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='EmployeeEvaluation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('evaluation_date', models.DateField(verbose_name='تاريخ التقييم')),
                ('overall_rating', models.DecimalField(decimal_places=2, max_digits=3, verbose_name='التقييم العام')),
                ('comments', models.TextField(blank=True, null=True, verbose_name='التعليقات')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Hr.employee', verbose_name='الموظف')),
                ('evaluator', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='المقيم')),
            ],
            options={
                'verbose_name': 'تقييم الموظف',
                'verbose_name_plural': 'تقييمات الموظفين',
                'db_table': 'Tbl_Employee_Evaluation',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='EmployeeFile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=250, verbose_name='العنوان')),
                ('file', models.FileField(upload_to='employee_files/', verbose_name='الملف')),
                ('file_type', models.CharField(max_length=50, verbose_name='نوع الملف')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Hr.employee', verbose_name='الموظف')),
                ('uploaded_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='رفع بواسطة')),
            ],
            options={
                'verbose_name': 'ملف الموظف',
                'verbose_name_plural': 'ملفات الموظفين',
                'db_table': 'Tbl_Employee_File',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='EmployeeLeave',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('start_date', models.DateField(verbose_name='تاريخ البداية')),
                ('end_date', models.DateField(verbose_name='تاريخ النهاية')),
                ('reason', models.TextField(verbose_name='السبب')),
                ('status', models.CharField(max_length=50, verbose_name='الحالة')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Hr.employee', verbose_name='الموظف')),
                ('leave_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Hr.leavetype', verbose_name='نوع الإجازة')),
            ],
            options={
                'verbose_name': 'إجازة الموظف',
                'verbose_name_plural': 'إجازات الموظفين',
                'db_table': 'Tbl_Employee_Leave',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='EmployeeNote',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=250, verbose_name='العنوان')),
                ('content', models.TextField(verbose_name='المحتوى')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Hr.employee', verbose_name='الموظف')),
            ],
            options={
                'verbose_name': 'ملاحظة الموظف',
                'verbose_name_plural': 'ملاحظات الموظفين',
                'db_table': 'Tbl_Employee_Note',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='EmployeeNoteHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('old_content', models.TextField(verbose_name='المحتوى القديم')),
                ('new_content', models.TextField(verbose_name='المحتوى الجديد')),
                ('changed_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ التغيير')),
                ('changed_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='غير بواسطة')),
                ('note', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Hr.employeenote', verbose_name='الملاحظة')),
            ],
            options={
                'verbose_name': 'تاريخ ملاحظة الموظف',
                'verbose_name_plural': 'تاريخ ملاحظات الموظفين',
                'db_table': 'Tbl_Employee_Note_History',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='HrAttendanceRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('record_date', models.DateField(verbose_name='تاريخ التسجيل')),
                ('record_time', models.TimeField(verbose_name='وقت التسجيل')),
                ('record_type', models.CharField(choices=[('in', 'حضور'), ('out', 'انصراف')], max_length=5, verbose_name='نوع التسجيل')),
                ('source', models.CharField(choices=[('machine', 'ماكينة'), ('manual', 'يدوي')], default='machine', max_length=10, verbose_name='المصدر')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='hr_attendance_records', to='Hr.employee', verbose_name='الموظف')),
                ('machine', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='records', to='Hr.hrattendancemachine', verbose_name='الماكينة')),
            ],
            options={
                'verbose_name': 'سجل الحضور',
                'verbose_name_plural': 'سجلات الحضور',
                'db_table': 'Hr_AttendanceRecord',
                'ordering': ['record_date', 'record_time'],
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='HrEmployeeAttendanceRule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('effective_date', models.DateField(verbose_name='تاريخ السريان')),
                ('end_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الانتهاء')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('attendance_rule', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='employees', to='Hr.hrattendancerule', verbose_name='قاعدة الحضور')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attendance_rules', to='Hr.employee', verbose_name='الموظف')),
            ],
            options={
                'verbose_name': 'قاعدة حضور الموظف',
                'verbose_name_plural': 'قواعد حضور الموظفين',
                'db_table': 'Hr_EmployeeAttendanceRule',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='HrEmployeeNote',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='عنوان الملاحظة')),
                ('content', models.TextField(help_text='اكتب تفاصيل الملاحظة هنا', verbose_name='محتوى الملاحظة')),
                ('note_type', models.CharField(choices=[('positive', 'إيجابية/جيدة'), ('negative', 'سلبية/ضعيفة'), ('general', 'عامة/محايدة')], default='general', max_length=20, verbose_name='نوع الملاحظة')),
                ('priority', models.CharField(choices=[('low', 'منخفضة'), ('medium', 'متوسطة'), ('high', 'عالية'), ('urgent', 'عاجلة')], default='medium', max_length=20, verbose_name='الأولوية')),
                ('evaluation_link', models.CharField(blank=True, help_text='رابط اختياري لربط الملاحظة بتقييم الأداء', max_length=500, null=True, verbose_name='رابط التقييم')),
                ('evaluation_score', models.DecimalField(blank=True, decimal_places=2, help_text='درجة التقييم المرتبطة بالملاحظة (اختياري)', max_digits=5, null=True, verbose_name='درجة التقييم')),
                ('is_important', models.BooleanField(default=False, verbose_name='ملاحظة مهمة')),
                ('is_confidential', models.BooleanField(default=False, help_text='ملاحظة سرية - محدودة الوصول', verbose_name='سرية')),
                ('is_active', models.BooleanField(default=True, help_text='إلغاء تفعيل الملاحظة بدلاً من حذفها', verbose_name='نشطة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ آخر تحديث')),
                ('tags', models.CharField(blank=True, help_text='علامات مفصولة بفواصل للبحث والتصنيف', max_length=500, null=True, verbose_name='العلامات')),
                ('follow_up_required', models.BooleanField(default=False, verbose_name='يتطلب متابعة')),
                ('follow_up_date', models.DateField(blank=True, null=True, verbose_name='تاريخ المتابعة')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_employee_notes', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='employee_notes', to='Hr.employee', verbose_name='الموظف')),
                ('last_modified_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='modified_employee_notes', to=settings.AUTH_USER_MODEL, verbose_name='آخر تعديل بواسطة')),
            ],
            options={
                'verbose_name': 'ملاحظة الموظف',
                'verbose_name_plural': 'ملاحظات الموظفين',
                'ordering': ['-created_at'],
                'permissions': [('view_confidential_notes', 'يمكن عرض الملاحظات السرية'), ('manage_all_notes', 'يمكن إدارة جميع الملاحظات')],
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='HrEmployeeNoteHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action', models.CharField(choices=[('created', 'تم الإنشاء'), ('updated', 'تم التحديث'), ('deleted', 'تم الحذف'), ('restored', 'تم الاستعادة')], max_length=20, verbose_name='الإجراء')),
                ('changed_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ التغيير')),
                ('old_values', models.JSONField(blank=True, null=True, verbose_name='القيم السابقة')),
                ('new_values', models.JSONField(blank=True, null=True, verbose_name='القيم الجديدة')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات التغيير')),
                ('changed_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='تم التغيير بواسطة')),
                ('note', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='history', to='Hr.hremployeenote', verbose_name='الملاحظة')),
            ],
            options={
                'verbose_name': 'تاريخ ملاحظة الموظف',
                'verbose_name_plural': 'تاريخ ملاحظات الموظفين',
                'ordering': ['-changed_at'],
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='HrEmployeeTask',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='عنوان المهمة')),
                ('description', models.TextField(verbose_name='وصف المهمة')),
                ('status', models.CharField(choices=[('pending', 'قيد الانتظار'), ('in_progress', 'قيد التنفيذ'), ('completed', 'مكتملة'), ('cancelled', 'ملغاة')], default='pending', max_length=20, verbose_name='الحالة')),
                ('priority', models.CharField(choices=[('low', 'منخفضة'), ('medium', 'متوسطة'), ('high', 'عالية'), ('urgent', 'عاجلة')], default='medium', max_length=20, verbose_name='الأولوية')),
                ('start_date', models.DateField(verbose_name='تاريخ البداية')),
                ('due_date', models.DateField(verbose_name='تاريخ الاستحقاق')),
                ('completion_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الإنجاز')),
                ('progress', models.PositiveIntegerField(default=0, help_text='من 0 إلى 100', verbose_name='نسبة الإنجاز (%)')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('assigned_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='hr_assigned_tasks', to=settings.AUTH_USER_MODEL, verbose_name='تم التكليف بواسطة')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tasks', to='Hr.employee', verbose_name='الموظف')),
            ],
            options={
                'verbose_name': 'مهمة الموظف',
                'verbose_name_plural': 'مهام الموظفين',
                'ordering': ['-due_date', 'priority'],
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='HrJob',
            fields=[
                ('jop_code', models.IntegerField(db_column='Jop_Code', primary_key=True, serialize=False, verbose_name='رمز الوظيفة')),
                ('jop_name', models.CharField(db_column='Jop_Name', max_length=50, verbose_name='اسم الوظيفة')),
                ('department', models.ForeignKey(blank=True, db_column='Dept_Code', null=True, on_delete=django.db.models.deletion.SET_NULL, to='Hr.department', verbose_name='القسم')),
            ],
            options={
                'verbose_name': 'الوظيفة',
                'verbose_name_plural': 'الوظائف',
                'db_table': 'Tbl_Jop',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='HrOfficialHoliday',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم الإجازة')),
                ('date', models.DateField(verbose_name='تاريخ الإجازة')),
                ('description', models.TextField(blank=True, null=True, verbose_name='وصف')),
                ('is_recurring', models.BooleanField(default=False, verbose_name='إجازة متكررة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'إجازة رسمية',
                'verbose_name_plural': 'إجازات رسمية',
                'db_table': 'Hr_OfficialHoliday',
                'managed': True,
                'unique_together': {('name', 'date')},
            },
        ),
        migrations.CreateModel(
            name='HrPickupPoint',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='اسم النقطة')),
                ('address', models.CharField(max_length=255, verbose_name='العنوان')),
                ('coordinates', models.CharField(blank=True, max_length=100, null=True, verbose_name='الإحداثيات')),
                ('description', models.TextField(blank=True, null=True, verbose_name='وصف')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('car', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='pickup_points', to='Hr.hrcar', verbose_name='السيارة')),
            ],
            options={
                'verbose_name': 'نقطة تجمع',
                'verbose_name_plural': 'نقاط التجمع',
                'db_table': 'Hr_PickupPoint',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='HrTask',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=250, verbose_name='العنوان')),
                ('due_date', models.DateField(verbose_name='تاريخ الاستحقاق')),
                ('status', models.CharField(max_length=50, verbose_name='الحالة')),
                ('priority', models.CharField(max_length=50, verbose_name='الأولوية')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('assigned_to', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='أسند إلى')),
            ],
            options={
                'verbose_name': 'مهمة الموارد البشرية',
                'verbose_name_plural': 'مهام الموارد البشرية',
                'db_table': 'Tbl_Hr_Task',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='HrTaskNew',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='عنوان المهمة')),
                ('description', models.TextField(verbose_name='وصف المهمة')),
                ('task_type', models.CharField(choices=[('insurance', 'متابعة التأمينات'), ('transportation', 'متابعة بدل المواصلات'), ('car_issues', 'مشاكل سيارات النقل'), ('contract_renewal', 'تجديد العقود'), ('other', 'أخرى')], default='other', max_length=20, verbose_name='نوع المهمة')),
                ('status', models.CharField(choices=[('pending', 'قيد الانتظار'), ('in_progress', 'قيد التنفيذ'), ('completed', 'مكتملة'), ('cancelled', 'ملغاة')], default='pending', max_length=20, verbose_name='الحالة')),
                ('priority', models.CharField(choices=[('low', 'منخفضة'), ('medium', 'متوسطة'), ('high', 'عالية'), ('urgent', 'عاجلة')], default='medium', max_length=20, verbose_name='الأولوية')),
                ('start_date', models.DateField(verbose_name='تاريخ البداية')),
                ('due_date', models.DateField(verbose_name='تاريخ الاستحقاق')),
                ('completion_date', models.DateField(blank=True, null=True, verbose_name='تاريخ الإنجاز')),
                ('progress', models.PositiveIntegerField(default=0, help_text='من 0 إلى 100', verbose_name='نسبة الإنجاز (%)')),
                ('steps_taken', models.TextField(blank=True, null=True, verbose_name='الخطوات المتخذة')),
                ('reminder_days', models.PositiveIntegerField(default=3, verbose_name='أيام التذكير قبل الموعد')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('assigned_to', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='hr_tasks', to=settings.AUTH_USER_MODEL, verbose_name='تم التكليف إلى')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_hr_tasks', to=settings.AUTH_USER_MODEL, verbose_name='تم الإنشاء بواسطة')),
            ],
            options={
                'verbose_name': 'مهمة الموارد البشرية',
                'verbose_name_plural': 'مهام الموارد البشرية',
                'ordering': ['-due_date', 'priority'],
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='PayrollPeriod',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('period', models.CharField(max_length=50, verbose_name='الفترة')),
                ('status', models.CharField(max_length=50, verbose_name='الحالة')),
                ('total_amount', models.DecimalField(decimal_places=2, default=0, max_digits=15, verbose_name='إجمالي المبلغ')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_payroll_periods', to=settings.AUTH_USER_MODEL, verbose_name='اعتمد بواسطة')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_payroll_periods', to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
            ],
            options={
                'verbose_name': 'فترة الرواتب',
                'verbose_name_plural': 'فترات الرواتب',
                'db_table': 'Tbl_Payroll_Period',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='PayrollEntry',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='إجمالي المبلغ')),
                ('status', models.CharField(max_length=50, verbose_name='الحالة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Hr.employee', verbose_name='الموظف')),
                ('period', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Hr.payrollperiod', verbose_name='الفترة')),
            ],
            options={
                'verbose_name': 'قيد الراتب',
                'verbose_name_plural': 'قيود الرواتب',
                'db_table': 'Tbl_Payroll_Entry',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='PickupPoint',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('location', models.CharField(max_length=250, verbose_name='الموقع')),
                ('description', models.TextField(blank=True, null=True, verbose_name='الوصف')),
                ('car', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Hr.car', verbose_name='السيارة')),
            ],
            options={
                'verbose_name': 'نقطة الالتقاط',
                'verbose_name_plural': 'نقاط الالتقاط',
                'db_table': 'Tbl_Pickup_Point',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='PayrollItemDetail',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='المبلغ')),
                ('payroll_entry', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Hr.payrollentry', verbose_name='قيد الراتب')),
                ('salary_item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Hr.salaryitem', verbose_name='بند الراتب')),
            ],
            options={
                'verbose_name': 'تفاصيل بند الراتب',
                'verbose_name_plural': 'تفاصيل بنود الرواتب',
                'db_table': 'Tbl_Payroll_Item_Detail',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='EmployeeSalaryItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='المبلغ')),
                ('start_date', models.DateField(verbose_name='تاريخ البداية')),
                ('end_date', models.DateField(blank=True, null=True, verbose_name='تاريخ النهاية')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Hr.employee', verbose_name='الموظف')),
                ('salary_item', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Hr.salaryitem', verbose_name='بند الراتب')),
            ],
            options={
                'verbose_name': 'بند راتب الموظف',
                'verbose_name_plural': 'بنود رواتب الموظفين',
                'db_table': 'Tbl_Employee_Salary_Item',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='HrAttendanceSummary',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(verbose_name='التاريخ')),
                ('status', models.CharField(choices=[('present', 'حاضر'), ('absent', 'غائب'), ('late', 'متأخر'), ('early_leave', 'انصراف مبكر'), ('holiday', 'إجازة'), ('weekend', 'عطلة أسبوعية')], max_length=20, verbose_name='الحالة')),
                ('time_in', models.TimeField(blank=True, null=True, verbose_name='وقت الحضور')),
                ('time_out', models.TimeField(blank=True, null=True, verbose_name='وقت الانصراف')),
                ('late_minutes', models.PositiveIntegerField(default=0, verbose_name='دقائق التأخير')),
                ('early_leave_minutes', models.PositiveIntegerField(default=0, verbose_name='دقائق الانصراف المبكر')),
                ('overtime_minutes', models.PositiveIntegerField(default=0, verbose_name='دقائق العمل الإضافي')),
                ('working_minutes', models.PositiveIntegerField(default=0, verbose_name='دقائق العمل')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='hr_attendance_summaries', to='Hr.employee', verbose_name='الموظف')),
            ],
            options={
                'verbose_name': 'ملخص الحضور',
                'verbose_name_plural': 'ملخصات الحضور',
                'db_table': 'Hr_AttendanceSummary',
                'ordering': ['date'],
                'managed': True,
                'unique_together': {('employee', 'date')},
            },
        ),
    ]
